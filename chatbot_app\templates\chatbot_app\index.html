{% extends 'base.html' %}

{% block title %}AI Agent Chatbot - Company Manual Assistant{% endblock %}

{% block content %}
<div class="row">
    <!-- Document Upload Section -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-upload me-2"></i>Upload Documents</h5>
            </div>
            <div class="card-body">
                <div class="file-upload-area" id="fileUploadArea">
                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                    <p class="mb-2">Drag & drop files here or click to browse</p>
                    <p class="text-muted small">Supported: PDF, DOCX, TXT, MD (Max 10MB)</p>
                    <input type="file" id="fileInput" class="d-none" multiple accept=".pdf,.docx,.txt,.md">
                    <button class="btn btn-outline-primary" onclick="document.getElementById('fileInput').click()">
                        Choose Files
                    </button>
                </div>
                
                <div id="uploadProgress" class="mt-3" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted">Uploading...</small>
                </div>
            </div>
        </div>
        
        <!-- Document List -->
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-file-alt me-2"></i>Uploaded Documents</h6>
            </div>
            <div class="card-body">
                <div id="documentList">
                    <p class="text-muted">No documents uploaded yet.</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Chat Section -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-comments me-2"></i>Chat with AI Assistant</h5>
                <button class="btn btn-sm btn-outline-secondary" onclick="clearChat()">
                    <i class="fas fa-trash me-1"></i>Clear Chat
                </button>
            </div>
            <div class="card-body p-0">
                <div class="chat-container p-3" id="chatContainer">
                    <div class="message assistant-message">
                        <strong>AI Assistant:</strong> Hello! I'm here to help you with questions about your company documents. Please upload some documents first, then feel free to ask me anything!
                    </div>
                </div>
                
                <div class="typing-indicator" id="typingIndicator">
                    <i class="fas fa-circle-notch fa-spin me-2"></i>AI is typing...
                </div>
                
                <div class="p-3 border-top">
                    <div class="input-group">
                        <input type="text" class="form-control" id="messageInput" 
                               placeholder="Ask a question about your documents..." 
                               onkeypress="handleKeyPress(event)">
                        <button class="btn btn-primary" onclick="sendMessage()">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentSessionId = null;

// File upload handling
document.getElementById('fileInput').addEventListener('change', handleFileSelect);
document.getElementById('fileUploadArea').addEventListener('click', () => {
    document.getElementById('fileInput').click();
});

// Drag and drop handling
const uploadArea = document.getElementById('fileUploadArea');
uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', () => {
    uploadArea.classList.remove('dragover');
});

uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    const files = e.dataTransfer.files;
    handleFiles(files);
});

function handleFileSelect(event) {
    const files = event.target.files;
    handleFiles(files);
}

function handleFiles(files) {
    for (let file of files) {
        uploadFile(file);
    }
}

async function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('title', file.name);
    
    const progressDiv = document.getElementById('uploadProgress');
    const progressBar = progressDiv.querySelector('.progress-bar');
    
    progressDiv.style.display = 'block';
    
    try {
        const response = await axios.post('/api/documents/', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: (progressEvent) => {
                const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                progressBar.style.width = percentCompleted + '%';
            }
        });
        
        addMessage('system', `Document "${file.name}" uploaded successfully and is being processed.`);
        loadDocuments();
        
    } catch (error) {
        console.error('Upload error:', error);
        addMessage('system', `Failed to upload "${file.name}". Please try again.`);
    } finally {
        progressDiv.style.display = 'none';
        progressBar.style.width = '0%';
    }
}

async function loadDocuments() {
    try {
        const response = await axios.get('/api/documents/');
        const documents = response.data;
        
        const documentList = document.getElementById('documentList');
        
        if (documents.length === 0) {
            documentList.innerHTML = '<p class="text-muted">No documents uploaded yet.</p>';
            return;
        }
        
        let html = '';
        documents.forEach(doc => {
            const statusIcon = doc.processed ? 'fas fa-check-circle text-success' : 'fas fa-clock text-warning';
            const statusText = doc.processed ? 'Processed' : 'Processing...';
            
            html += `
                <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                    <div>
                        <small class="fw-bold">${doc.title}</small><br>
                        <small class="text-muted">
                            <i class="${statusIcon} me-1"></i>${statusText}
                            ${doc.chunk_count > 0 ? `(${doc.chunk_count} chunks)` : ''}
                        </small>
                    </div>
                    <small class="text-muted">${doc.file_type.toUpperCase()}</small>
                </div>
            `;
        });
        
        documentList.innerHTML = html;
        
    } catch (error) {
        console.error('Error loading documents:', error);
    }
}

function handleKeyPress(event) {
    if (event.key === 'Enter') {
        sendMessage();
    }
}

async function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();
    
    if (!message) return;
    
    // Add user message to chat
    addMessage('user', message);
    messageInput.value = '';
    
    // Show typing indicator
    showTypingIndicator();
    
    try {
        const response = await axios.post('/api/chat/', {
            message: message,
            session_id: currentSessionId,
            include_sources: true,
            max_sources: 3
        });
        
        const data = response.data;
        currentSessionId = data.session_id;
        
        // Add assistant response
        addMessage('assistant', data.response, data.sources);
        
    } catch (error) {
        console.error('Chat error:', error);
        addMessage('assistant', 'Sorry, I encountered an error processing your request. Please try again.');
    } finally {
        hideTypingIndicator();
    }
}

function addMessage(type, content, sources = []) {
    const chatContainer = document.getElementById('chatContainer');
    const messageDiv = document.createElement('div');
    
    const messageClass = type === 'user' ? 'user-message' : 
                        type === 'system' ? 'alert alert-info' : 'assistant-message';
    
    messageDiv.className = `message ${messageClass}`;
    
    let html = `<strong>${type === 'user' ? 'You' : type === 'system' ? 'System' : 'AI Assistant'}:</strong> ${content}`;
    
    // Add sources if available
    if (sources && sources.length > 0) {
        html += '<div class="mt-2">';
        sources.forEach((source, index) => {
            html += `
                <div class="source-card">
                    <strong>Source ${index + 1}:</strong> ${source.document_title}<br>
                    <small>${source.content_preview}</small><br>
                    <small class="text-muted">Relevance: ${(source.similarity_score * 100).toFixed(1)}%</small>
                </div>
            `;
        });
        html += '</div>';
    }
    
    messageDiv.innerHTML = html;
    chatContainer.appendChild(messageDiv);
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

function showTypingIndicator() {
    document.getElementById('typingIndicator').style.display = 'block';
}

function hideTypingIndicator() {
    document.getElementById('typingIndicator').style.display = 'none';
}

function clearChat() {
    const chatContainer = document.getElementById('chatContainer');
    chatContainer.innerHTML = `
        <div class="message assistant-message">
            <strong>AI Assistant:</strong> Hello! I'm here to help you with questions about your company documents. Please upload some documents first, then feel free to ask me anything!
        </div>
    `;
    currentSessionId = null;
}

// Load documents on page load
document.addEventListener('DOMContentLoaded', () => {
    loadDocuments();
    
    // Refresh document list every 10 seconds to check processing status
    setInterval(loadDocuments, 10000);
});
</script>
{% endblock %}
