from celery import shared_task
from django.conf import settings
import logging
import os
from .models import Document, DocumentChunk
from .services import DocumentService
from .chromadb_utils import ChromaDBManager

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def process_document_task(self, document_id):
    """
    Background task to process uploaded documents
    """
    try:
        # Get the document
        document = Document.objects.get(id=document_id)
        document.processing_status = 'processing'
        document.save()
        
        logger.info(f"Starting to process document: {document.title}")
        
        # Initialize services
        document_service = DocumentService()
        chroma_manager = ChromaDBManager()
        
        # Extract text from document
        file_path = document.file.path
        text_content = document_service.extract_text(file_path, document.file_type)
        
        if not text_content:
            raise Exception("Failed to extract text from document")
        
        # Update document with preview
        document.content_preview = text_content[:500] + "..." if len(text_content) > 500 else text_content
        document.save()
        
        # Split text into chunks
        chunks = document_service.split_text_into_chunks(text_content)
        
        logger.info(f"Split document into {len(chunks)} chunks")
        
        # Delete existing chunks for this document
        DocumentChunk.objects.filter(document=document).delete()
        
        # Process each chunk
        chunk_data = []
        for i, chunk in enumerate(chunks):
            # Create document chunk record
            doc_chunk = DocumentChunk.objects.create(
                document=document,
                chunk_index=i,
                content=chunk,
                metadata={
                    'document_title': document.title,
                    'file_type': document.file_type,
                    'chunk_length': len(chunk),
                    'upload_date': document.uploaded_at.isoformat()
                }
            )
            
            chunk_data.append({
                'id': str(doc_chunk.id),
                'content': chunk,
                'metadata': doc_chunk.metadata
            })
        
        # Store chunks in ChromaDB
        chroma_manager.add_documents(chunk_data)
        
        # Update document status
        document.processed = True
        document.processing_status = 'completed'
        document.chunk_count = len(chunks)
        document.save()
        
        logger.info(f"Successfully processed document: {document.title}")
        
        return {
            'status': 'success',
            'document_id': str(document_id),
            'chunks_created': len(chunks)
        }
        
    except Document.DoesNotExist:
        logger.error(f"Document with id {document_id} not found")
        return {'status': 'error', 'message': 'Document not found'}
        
    except Exception as e:
        logger.error(f"Error processing document {document_id}: {str(e)}")
        
        # Update document status to failed
        try:
            document = Document.objects.get(id=document_id)
            document.processing_status = 'failed'
            document.save()
        except:
            pass
        
        # Retry the task
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying document processing for {document_id}")
            raise self.retry(countdown=60 * (self.request.retries + 1))
        
        return {'status': 'error', 'message': str(e)}


@shared_task
def cleanup_old_sessions():
    """
    Background task to cleanup old inactive chat sessions
    """
    from django.utils import timezone
    from datetime import timedelta
    
    try:
        # Delete sessions older than 30 days that are inactive
        cutoff_date = timezone.now() - timedelta(days=30)
        
        from .models import ChatSession
        old_sessions = ChatSession.objects.filter(
            is_active=False,
            updated_at__lt=cutoff_date
        )
        
        count = old_sessions.count()
        old_sessions.delete()
        
        logger.info(f"Cleaned up {count} old chat sessions")
        return {'status': 'success', 'cleaned_sessions': count}
        
    except Exception as e:
        logger.error(f"Error cleaning up old sessions: {str(e)}")
        return {'status': 'error', 'message': str(e)}


@shared_task
def update_embeddings():
    """
    Background task to update embeddings for all documents
    """
    try:
        chroma_manager = ChromaDBManager()
        document_service = DocumentService()
        
        # Get all processed documents
        documents = Document.objects.filter(processed=True)
        
        updated_count = 0
        for document in documents:
            try:
                # Get all chunks for this document
                chunks = DocumentChunk.objects.filter(document=document)
                
                chunk_data = []
                for chunk in chunks:
                    chunk_data.append({
                        'id': str(chunk.id),
                        'content': chunk.content,
                        'metadata': chunk.metadata
                    })
                
                # Update embeddings in ChromaDB
                chroma_manager.update_documents(chunk_data)
                updated_count += 1
                
            except Exception as e:
                logger.error(f"Error updating embeddings for document {document.id}: {str(e)}")
                continue
        
        logger.info(f"Updated embeddings for {updated_count} documents")
        return {'status': 'success', 'updated_documents': updated_count}
        
    except Exception as e:
        logger.error(f"Error updating embeddings: {str(e)}")
        return {'status': 'error', 'message': str(e)}
