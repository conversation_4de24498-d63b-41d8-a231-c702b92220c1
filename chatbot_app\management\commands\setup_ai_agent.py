from django.core.management.base import BaseCommand
from django.conf import settings
import os
import sys


class Command(BaseCommand):
    help = 'Setup AI Agent Chatbot - Create necessary directories and check configuration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--check-only',
            action='store_true',
            help='Only check configuration without creating directories',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up AI Agent Chatbot...'))
        
        # Check configuration
        self.check_configuration()
        
        if not options['check_only']:
            # Create necessary directories
            self.create_directories()
            
            # Initialize ChromaDB
            self.initialize_chromadb()
        
        self.stdout.write(self.style.SUCCESS('Setup completed successfully!'))

    def check_configuration(self):
        """Check if all required configuration is present"""
        self.stdout.write('Checking configuration...')
        
        required_settings = [
            'SECRET_KEY',
            'OPENAI_API_KEY',
            'CHROMA_DB_PATH',
            'CHROMA_COLLECTION_NAME',
        ]
        
        missing_settings = []
        
        for setting in required_settings:
            if not hasattr(settings, setting) or not getattr(settings, setting):
                missing_settings.append(setting)
        
        if missing_settings:
            self.stdout.write(
                self.style.ERROR(f'Missing required settings: {", ".join(missing_settings)}')
            )
            self.stdout.write('Please check your settings.py or .env file')
            sys.exit(1)
        
        # Check OpenAI API key format
        api_key = settings.OPENAI_API_KEY
        if not api_key.startswith('sk-'):
            self.stdout.write(
                self.style.WARNING('OpenAI API key format looks incorrect (should start with "sk-")')
            )
        
        self.stdout.write(self.style.SUCCESS('Configuration check passed!'))

    def create_directories(self):
        """Create necessary directories"""
        self.stdout.write('Creating directories...')
        
        directories = [
            settings.MEDIA_ROOT,
            settings.STATIC_ROOT,
            settings.CHROMA_DB_PATH,
            os.path.join(settings.MEDIA_ROOT, 'documents'),
        ]
        
        for directory in directories:
            if directory and not os.path.exists(directory):
                try:
                    os.makedirs(directory, exist_ok=True)
                    self.stdout.write(f'Created directory: {directory}')
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'Failed to create directory {directory}: {e}')
                    )
        
        self.stdout.write(self.style.SUCCESS('Directories created successfully!'))

    def initialize_chromadb(self):
        """Initialize ChromaDB"""
        self.stdout.write('Initializing ChromaDB...')
        
        try:
            from chromadb_utils import ChromaDBManager
            
            chroma_manager = ChromaDBManager()
            collection_info = chroma_manager.get_collection_info()
            
            self.stdout.write(f'ChromaDB collection "{collection_info["name"]}" initialized')
            self.stdout.write(f'Current document count: {collection_info["count"]}')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to initialize ChromaDB: {e}')
            )
            self.stdout.write('Make sure all dependencies are installed correctly')

    def check_dependencies(self):
        """Check if all required dependencies are installed"""
        self.stdout.write('Checking dependencies...')
        
        required_packages = [
            'django',
            'djangorestframework',
            'openai',
            'chromadb',
            'PyPDF2',
            'python-docx',
            'celery',
            'redis',
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            self.stdout.write(
                self.style.ERROR(f'Missing packages: {", ".join(missing_packages)}')
            )
            self.stdout.write('Run: pip install -r requirements.txt')
            sys.exit(1)
        
        self.stdout.write(self.style.SUCCESS('All dependencies are installed!'))
