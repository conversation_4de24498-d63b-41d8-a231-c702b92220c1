from django.contrib import admin
from .models import Document, ChatSession, ChatMessage, DocumentChunk, SystemConfiguration


@admin.register(Document)
class DocumentAdmin(admin.ModelAdmin):
    list_display = ['title', 'file_type', 'uploaded_by', 'uploaded_at', 'processing_status', 'processed', 'chunk_count']
    list_filter = ['file_type', 'processing_status', 'processed', 'uploaded_at']
    search_fields = ['title', 'content_preview']
    readonly_fields = ['id', 'uploaded_at', 'chunk_count']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'title', 'file', 'file_type', 'uploaded_by', 'uploaded_at')
        }),
        ('Processing Status', {
            'fields': ('processing_status', 'processed', 'chunk_count')
        }),
        ('Content', {
            'fields': ('content_preview',),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['reprocess_documents', 'mark_as_processed']
    
    def reprocess_documents(self, request, queryset):
        """Admin action to reprocess selected documents"""
        from .tasks import process_document_task
        
        count = 0
        for document in queryset:
            document.processing_status = 'pending'
            document.processed = False
            document.save()
            
            try:
                process_document_task.delay(document.id)
                count += 1
            except Exception:
                pass
        
        self.message_user(request, f"Started reprocessing {count} documents.")
    
    def mark_as_processed(self, request, queryset):
        """Admin action to mark documents as processed"""
        updated = queryset.update(processed=True, processing_status='completed')
        self.message_user(request, f"Marked {updated} documents as processed.")
    
    reprocess_documents.short_description = "Reprocess selected documents"
    mark_as_processed.short_description = "Mark as processed"


@admin.register(ChatSession)
class ChatSessionAdmin(admin.ModelAdmin):
    list_display = ['id', 'session_name', 'user', 'created_at', 'updated_at', 'is_active', 'message_count']
    list_filter = ['is_active', 'created_at', 'updated_at']
    search_fields = ['session_name', 'user__username']
    readonly_fields = ['id', 'created_at', 'updated_at', 'message_count']
    
    def message_count(self, obj):
        return obj.messages.count()
    message_count.short_description = 'Messages'


@admin.register(ChatMessage)
class ChatMessageAdmin(admin.ModelAdmin):
    list_display = ['id', 'session', 'message_type', 'content_preview', 'timestamp', 'confidence_score']
    list_filter = ['message_type', 'timestamp']
    search_fields = ['content', 'session__session_name']
    readonly_fields = ['id', 'timestamp']
    
    def content_preview(self, obj):
        return obj.content[:100] + "..." if len(obj.content) > 100 else obj.content
    content_preview.short_description = 'Content Preview'


@admin.register(DocumentChunk)
class DocumentChunkAdmin(admin.ModelAdmin):
    list_display = ['id', 'document', 'chunk_index', 'content_preview', 'created_at']
    list_filter = ['document__file_type', 'created_at']
    search_fields = ['content', 'document__title']
    readonly_fields = ['id', 'created_at']
    
    def content_preview(self, obj):
        return obj.content[:100] + "..." if len(obj.content) > 100 else obj.content
    content_preview.short_description = 'Content Preview'


@admin.register(SystemConfiguration)
class SystemConfigurationAdmin(admin.ModelAdmin):
    list_display = ['key', 'value_preview', 'description', 'updated_at']
    search_fields = ['key', 'description']
    readonly_fields = ['updated_at']
    
    def value_preview(self, obj):
        return obj.value[:100] + "..." if len(obj.value) > 100 else obj.value
    value_preview.short_description = 'Value Preview'
