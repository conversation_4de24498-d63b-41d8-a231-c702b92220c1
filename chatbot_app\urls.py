from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON>ult<PERSON><PERSON><PERSON>
from . import views

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'documents', views.DocumentViewSet)
router.register(r'sessions', views.ChatSessionViewSet)

app_name = 'chatbot_app'

urlpatterns = [
    # Main chatbot interface
    path('', views.index, name='index'),
    
    # API endpoints
    path('api/', include(router.urls)),
    path('api/chat/', views.chat_query, name='chat_query'),
    path('api/health/', views.health_check, name='health_check'),
]
