#!/usr/bin/env python
"""
Test script to verify the AI Agent setup and ChromaDB functionality
"""

import os
import sys
import django
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_agent_project.settings')

def test_django_setup():
    """Test Django setup"""
    print("Testing Django setup...")
    try:
        django.setup()
        print("✓ Django setup successful")
        return True
    except Exception as e:
        print(f"✗ Django setup failed: {e}")
        return False

def test_chromadb():
    """Test ChromaDB functionality"""
    print("\nTesting ChromaDB...")
    try:
        from chromadb_utils import ChromaDBManager
        
        # Initialize ChromaDB manager
        chroma_manager = ChromaDBManager()
        print("✓ ChromaDB manager initialized")
        
        # Test collection info
        collection_info = chroma_manager.get_collection_info()
        print(f"✓ Collection info: {collection_info}")
        
        # Test adding a simple document
        test_docs = [{
            'id': 'test_doc_1',
            'content': 'This is a test document for the AI agent chatbot system.',
            'metadata': {'source': 'test', 'type': 'sample'}
        }]
        
        print("Adding test document...")
        chroma_manager.add_documents(test_docs)
        print("✓ Test document added successfully")
        
        # Test searching
        print("Testing search functionality...")
        search_results = chroma_manager.search_similar_documents("test document", n_results=1)
        print(f"✓ Search results: {search_results}")
        
        # Clean up test document
        chroma_manager.delete_documents(['test_doc_1'])
        print("✓ Test document cleaned up")
        
        return True
        
    except Exception as e:
        print(f"✗ ChromaDB test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_document_processing():
    """Test document processing functionality"""
    print("\nTesting document processing...")
    try:
        from chatbot_app.services import DocumentService
        
        # Initialize document service
        doc_service = DocumentService()
        print("✓ Document service initialized")
        
        # Test text splitting
        test_text = "This is a test document. It has multiple sentences. We want to test the text splitting functionality. This should work correctly."
        chunks = doc_service.split_text_into_chunks(test_text)
        print(f"✓ Text split into {len(chunks)} chunks")
        
        return True
        
    except Exception as e:
        print(f"✗ Document processing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("AI Agent Chatbot Setup Test")
    print("=" * 40)
    
    # Test Django setup
    django_ok = test_django_setup()
    
    if not django_ok:
        print("\n❌ Django setup failed. Please check your configuration.")
        return False
    
    # Test ChromaDB
    chromadb_ok = test_chromadb()
    
    # Test document processing
    doc_processing_ok = test_document_processing()
    
    print("\n" + "=" * 40)
    print("Test Summary:")
    print(f"Django Setup: {'✓' if django_ok else '✗'}")
    print(f"ChromaDB: {'✓' if chromadb_ok else '✗'}")
    print(f"Document Processing: {'✓' if doc_processing_ok else '✗'}")
    
    if django_ok and chromadb_ok and doc_processing_ok:
        print("\n🎉 All tests passed! Your AI Agent setup is working correctly.")
        print("\nNext steps:")
        print("1. Add your OpenAI API key to the .env file")
        print("2. Run: python manage.py makemigrations")
        print("3. Run: python manage.py migrate")
        print("4. Run: python manage.py runserver")
        return True
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == '__main__':
    main()
