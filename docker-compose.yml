version: '3.8'

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ai_agent_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  web:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./media:/app/media
      - ./chroma_db:/app/chroma_db
    environment:
      - DEBUG=False
      - DATABASE_URL=**************************************/ai_agent_db
      - REDIS_URL=redis://redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - db
      - redis
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             gunicorn --bind 0.0.0.0:8000 --workers 3 ai_agent_project.wsgi:application"

  celery:
    build: .
    volumes:
      - ./media:/app/media
      - ./chroma_db:/app/chroma_db
    environment:
      - DEBUG=False
      - DATABASE_URL=**************************************/ai_agent_db
      - REDIS_URL=redis://redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - db
      - redis
    command: celery -A celery_app worker --loglevel=info

  celery-beat:
    build: .
    volumes:
      - ./media:/app/media
      - ./chroma_db:/app/chroma_db
    environment:
      - DEBUG=False
      - DATABASE_URL=**************************************/ai_agent_db
      - REDIS_URL=redis://redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - db
      - redis
    command: celery -A celery_app beat --loglevel=info

volumes:
  postgres_data:
  redis_data:
