#!/usr/bin/env python
"""
Simple Django test without complex imports
"""

import os
import sys
import django
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_agent_project.settings')

def test_django_models():
    """Test Django models creation"""
    print("Testing Django models...")
    
    try:
        # Setup Django
        django.setup()
        print("✓ Django setup successful")
        
        # Import models
        from chatbot_app.models import Document, ChatSession, ChatMessage
        print("✓ Models imported successfully")
        
        # Test model creation (without saving to DB)
        doc = Document(
            title="Test Document",
            file_type="pdf"
        )
        print("✓ Document model instance created")
        
        session = ChatSession(
            session_name="Test Session"
        )
        print("✓ ChatSession model instance created")
        
        message = ChatMessage(
            session=session,
            message_type="user",
            content="Test message"
        )
        print("✓ ChatMessage model instance created")
        
        return True
        
    except Exception as e:
        print(f"✗ Django models test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_creation():
    """Test database creation"""
    print("\nTesting database creation...")
    
    try:
        from django.core.management import execute_from_command_line
        
        # Run makemigrations
        print("Running makemigrations...")
        execute_from_command_line(['manage.py', 'makemigrations', 'chatbot_app'])
        print("✓ Migrations created")
        
        # Run migrate
        print("Running migrate...")
        execute_from_command_line(['manage.py', 'migrate'])
        print("✓ Database migrated")
        
        return True
        
    except Exception as e:
        print(f"✗ Database creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("Simple Django Test")
    print("=" * 30)
    
    # Test Django models
    models_ok = test_django_models()
    
    if models_ok:
        # Test database creation
        db_ok = test_database_creation()
    else:
        db_ok = False
    
    print("\n" + "=" * 30)
    print("Test Summary:")
    print(f"Django Models: {'✓' if models_ok else '✗'}")
    print(f"Database Setup: {'✓' if db_ok else '✗'}")
    
    if models_ok and db_ok:
        print("\n🎉 Django setup is working correctly!")
        return True
    else:
        print("\n❌ Some tests failed.")
        return False

if __name__ == '__main__':
    main()
