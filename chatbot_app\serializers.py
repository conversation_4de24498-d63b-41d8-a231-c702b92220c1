from rest_framework import serializers
from .models import Document, ChatSession, ChatMessage, DocumentChunk


class DocumentSerializer(serializers.ModelSerializer):
    """Serializer for Document model"""
    file_size = serializers.SerializerMethodField()
    
    class Meta:
        model = Document
        fields = [
            'id', 'title', 'file', 'file_type', 'uploaded_at', 
            'processed', 'processing_status', 'content_preview', 
            'chunk_count', 'file_size'
        ]
        read_only_fields = ['id', 'uploaded_at', 'processed', 'processing_status', 'chunk_count']
    
    def get_file_size(self, obj):
        if obj.file:
            return obj.file.size
        return 0


class DocumentUploadSerializer(serializers.ModelSerializer):
    """Serializer for document upload"""
    class Meta:
        model = Document
        fields = ['title', 'file']
    
    def validate_file(self, value):
        # Validate file type
        allowed_extensions = ['.pdf', '.txt', '.docx', '.md']
        file_extension = value.name.lower().split('.')[-1]
        if f'.{file_extension}' not in allowed_extensions:
            raise serializers.ValidationError(
                f"File type not supported. Allowed types: {', '.join(allowed_extensions)}"
            )
        
        # Validate file size (10MB limit)
        max_size = 10 * 1024 * 1024
        if value.size > max_size:
            raise serializers.ValidationError("File size cannot exceed 10MB")
        
        return value


class ChatMessageSerializer(serializers.ModelSerializer):
    """Serializer for ChatMessage model"""
    class Meta:
        model = ChatMessage
        fields = [
            'id', 'message_type', 'content', 'timestamp', 
            'sources', 'confidence_score'
        ]
        read_only_fields = ['id', 'timestamp']


class ChatSessionSerializer(serializers.ModelSerializer):
    """Serializer for ChatSession model"""
    messages = ChatMessageSerializer(many=True, read_only=True)
    message_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ChatSession
        fields = [
            'id', 'session_name', 'created_at', 'updated_at', 
            'is_active', 'messages', 'message_count'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_message_count(self, obj):
        return obj.messages.count()


class ChatQuerySerializer(serializers.Serializer):
    """Serializer for chat query requests"""
    message = serializers.CharField(max_length=2000)
    session_id = serializers.UUIDField(required=False)
    include_sources = serializers.BooleanField(default=True)
    max_sources = serializers.IntegerField(default=3, min_value=1, max_value=10)


class ChatResponseSerializer(serializers.Serializer):
    """Serializer for chat response"""
    response = serializers.CharField()
    session_id = serializers.UUIDField()
    sources = serializers.ListField(child=serializers.DictField(), required=False)
    confidence_score = serializers.FloatField(required=False)
    processing_time = serializers.FloatField(required=False)


class DocumentChunkSerializer(serializers.ModelSerializer):
    """Serializer for DocumentChunk model"""
    document_title = serializers.CharField(source='document.title', read_only=True)
    
    class Meta:
        model = DocumentChunk
        fields = [
            'id', 'document', 'document_title', 'chunk_index', 
            'content', 'metadata', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']
