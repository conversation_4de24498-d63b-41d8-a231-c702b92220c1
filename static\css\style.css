/* Custom styles for AI Agent Chatbot */

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-color);
}

/* Chat container styles */
.chat-container {
    height: 70vh;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: white;
    padding: 1rem;
}

.chat-container::-webkit-scrollbar {
    width: 6px;
}

.chat-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Message styles */
.message {
    margin-bottom: 1rem;
    padding: 0.75rem 1rem;
    border-radius: 1rem;
    max-width: 80%;
    word-wrap: break-word;
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.user-message {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    margin-left: auto;
    margin-right: 0;
    border-bottom-right-radius: 0.25rem;
}

.assistant-message {
    background-color: #e9ecef;
    color: #333;
    margin-right: auto;
    margin-left: 0;
    border-bottom-left-radius: 0.25rem;
    border: 1px solid #dee2e6;
}

.system-message {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
    margin: 0 auto;
    text-align: center;
    max-width: 60%;
}

/* Source card styles */
.source-card {
    background-color: #f8f9fa;
    border-left: 4px solid var(--primary-color);
    margin-top: 0.5rem;
    padding: 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.source-card:hover {
    background-color: #e9ecef;
    transform: translateX(2px);
}

.source-title {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.source-preview {
    color: #6c757d;
    font-size: 0.8rem;
    line-height: 1.4;
    margin-bottom: 0.25rem;
}

.source-relevance {
    font-size: 0.75rem;
    color: var(--secondary-color);
}

/* Typing indicator */
.typing-indicator {
    display: none;
    padding: 1rem;
    font-style: italic;
    color: var(--secondary-color);
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    margin: 0.5rem 0;
}

.typing-dots {
    display: inline-block;
}

.typing-dots::after {
    content: '';
    animation: dots 1.5s infinite;
}

@keyframes dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}

/* File upload area */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    background-color: white;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background-color: #f8f9fa;
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background-color: #e3f2fd;
    transform: scale(1.02);
}

.upload-icon {
    color: #6c757d;
    margin-bottom: 1rem;
}

/* Document list styles */
.document-item {
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
}

.document-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(0,123,255,0.1);
}

.document-status {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.status-processing {
    color: var(--warning-color);
}

.status-completed {
    color: var(--success-color);
}

.status-failed {
    color: var(--danger-color);
}

/* Progress bar */
.upload-progress {
    margin-top: 1rem;
}

.progress {
    height: 0.5rem;
    border-radius: 0.25rem;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color), #0056b3);
    transition: width 0.3s ease;
}

/* Input styles */
.message-input {
    border-radius: 1.5rem;
    border: 2px solid #dee2e6;
    padding: 0.75rem 1rem;
    transition: border-color 0.2s ease;
}

.message-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.send-button {
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    border: none;
    transition: all 0.2s ease;
}

.send-button:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
}

/* Card styles */
.custom-card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: box-shadow 0.2s ease;
}

.custom-card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.75rem 0.75rem 0 0 !important;
}

/* Responsive design */
@media (max-width: 768px) {
    .chat-container {
        height: 50vh;
    }
    
    .message {
        max-width: 95%;
    }
    
    .user-message {
        margin-left: 5%;
    }
    
    .assistant-message {
        margin-right: 5%;
    }
    
    .file-upload-area {
        padding: 1rem;
    }
}

/* Loading animation */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-soft {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(135deg, var(--primary-color), #0056b3) 1;
}
