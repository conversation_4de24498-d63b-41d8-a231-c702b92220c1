import openai
from django.conf import settings
import logging
from typing import List, Dict, Any
import PyPDF2
import docx
import re
from chromadb_utils import ChromaDBManager

logger = logging.getLogger(__name__)


class DocumentService:
    """
    Service class for document processing operations
    """
    
    def __init__(self):
        self.chunk_size = 1000
        self.chunk_overlap = 200
    
    def extract_text(self, file_path: str, file_type: str) -> str:
        """
        Extract text from different file types
        """
        try:
            if file_type.lower() == 'pdf':
                return self._extract_text_from_pdf(file_path)
            elif file_type.lower() == 'docx':
                return self._extract_text_from_docx(file_path)
            elif file_type.lower() in ['txt', 'md']:
                return self._extract_text_from_txt(file_path)
            else:
                raise ValueError(f"Unsupported file type: {file_type}")
                
        except Exception as e:
            logger.error(f"Failed to extract text from {file_path}: {e}")
            raise
    
    def _extract_text_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF file"""
        text = ""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {e}")
            raise
        return text.strip()
    
    def _extract_text_from_docx(self, file_path: str) -> str:
        """Extract text from DOCX file"""
        try:
            doc = docx.Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
        except Exception as e:
            logger.error(f"Error extracting text from DOCX: {e}")
            raise
        return text.strip()
    
    def _extract_text_from_txt(self, file_path: str) -> str:
        """Extract text from TXT/MD file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                text = file.read()
        except Exception as e:
            logger.error(f"Error extracting text from TXT/MD: {e}")
            raise
        return text.strip()
    
    def split_text_into_chunks(self, text: str) -> List[str]:
        """
        Split text into overlapping chunks
        """
        try:
            # Clean the text
            text = self._clean_text(text)
            
            # Split into sentences first
            sentences = re.split(r'[.!?]+', text)
            
            chunks = []
            current_chunk = ""
            
            for sentence in sentences:
                sentence = sentence.strip()
                if not sentence:
                    continue
                
                # Check if adding this sentence would exceed chunk size
                if len(current_chunk) + len(sentence) + 1 > self.chunk_size:
                    if current_chunk:
                        chunks.append(current_chunk.strip())
                        
                        # Create overlap by keeping last part of current chunk
                        overlap_words = current_chunk.split()[-self.chunk_overlap//10:]
                        current_chunk = " ".join(overlap_words) + " " + sentence
                    else:
                        # Sentence is too long, split it
                        words = sentence.split()
                        for i in range(0, len(words), self.chunk_size//10):
                            chunk_words = words[i:i + self.chunk_size//10]
                            chunks.append(" ".join(chunk_words))
                        current_chunk = ""
                else:
                    current_chunk += " " + sentence if current_chunk else sentence
            
            # Add the last chunk
            if current_chunk:
                chunks.append(current_chunk.strip())
            
            # Filter out very short chunks
            chunks = [chunk for chunk in chunks if len(chunk.strip()) > 50]
            
            return chunks
            
        except Exception as e:
            logger.error(f"Failed to split text into chunks: {e}")
            raise
    
    def _clean_text(self, text: str) -> str:
        """
        Clean and normalize text
        """
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but keep punctuation
        text = re.sub(r'[^\w\s.,!?;:()\-\'"]+', '', text)
        
        return text.strip()


class ChatService:
    """
    Service class for chat operations
    """
    
    def __init__(self):
        self.chroma_manager = ChromaDBManager()
        openai.api_key = settings.OPENAI_API_KEY
    
    def process_query(self, query: str, session_id: str = None, 
                     include_sources: bool = True, max_sources: int = 3) -> Dict[str, Any]:
        """
        Process a chat query and return response with sources
        """
        try:
            # Search for relevant documents
            search_results = self.chroma_manager.search_similar_documents(
                query=query,
                n_results=max_sources
            )
            
            # Prepare context from search results
            context = self._prepare_context(search_results['results'])
            
            # Generate response using OpenAI
            response = self._generate_response(query, context)
            
            # Prepare sources information
            sources = []
            if include_sources and search_results['results']:
                sources = self._format_sources(search_results['results'])
            
            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(search_results['results'])
            
            return {
                'response': response,
                'sources': sources,
                'confidence_score': confidence_score,
                'context_used': len(search_results['results']) > 0
            }
            
        except Exception as e:
            logger.error(f"Failed to process query: {e}")
            return {
                'response': "I apologize, but I'm having trouble processing your request right now. Please try again later.",
                'sources': [],
                'confidence_score': 0.0,
                'context_used': False
            }
    
    def _prepare_context(self, search_results: List[Dict[str, Any]]) -> str:
        """
        Prepare context from search results
        """
        if not search_results:
            return ""
        
        context_parts = []
        for i, result in enumerate(search_results):
            context_parts.append(f"Document {i+1}: {result['content']}")
        
        return "\n\n".join(context_parts)
    
    def _generate_response(self, query: str, context: str) -> str:
        """
        Generate response using OpenAI GPT
        """
        try:
            system_prompt = """You are a helpful AI assistant that answers questions based on company documentation. 
            Use the provided context to answer the user's question. If the context doesn't contain enough information 
            to answer the question, say so clearly. Be concise but comprehensive in your responses.
            
            Guidelines:
            - Always base your answers on the provided context
            - If information is not in the context, clearly state that
            - Be helpful and professional
            - Provide specific details when available
            - If asked about procedures, provide step-by-step instructions when possible
            """
            
            user_prompt = f"""Context from company documents:
            {context}
            
            User question: {query}
            
            Please provide a helpful answer based on the context above."""
            
            response = openai.ChatCompletion.create(
                model=settings.CHAT_MODEL,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=500,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Failed to generate response: {e}")
            return "I apologize, but I'm having trouble generating a response right now. Please try again later."
    
    def _format_sources(self, search_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Format source information for the response
        """
        sources = []
        for result in search_results:
            metadata = result.get('metadata', {})
            sources.append({
                'document_title': metadata.get('document_title', 'Unknown Document'),
                'content_preview': result['content'][:200] + "..." if len(result['content']) > 200 else result['content'],
                'similarity_score': round(result.get('similarity_score', 0), 3),
                'file_type': metadata.get('file_type', 'unknown')
            })
        
        return sources
    
    def _calculate_confidence_score(self, search_results: List[Dict[str, Any]]) -> float:
        """
        Calculate confidence score based on search results
        """
        if not search_results:
            return 0.0
        
        # Use the highest similarity score as base confidence
        max_similarity = max(result.get('similarity_score', 0) for result in search_results)
        
        # Adjust based on number of relevant results
        result_count_factor = min(len(search_results) / 3, 1.0)
        
        # Calculate final confidence score
        confidence = max_similarity * result_count_factor
        
        return round(confidence, 3)
