from django.shortcuts import render
from django.http import JsonResponse
from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view
from rest_framework.response import Response
from rest_framework.parsers import <PERSON>Part<PERSON><PERSON><PERSON>, FormParser
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import time
import logging

from .models import Document, ChatSession, ChatMessage, DocumentChunk
from .serializers import (
    DocumentSerializer, DocumentUploadSerializer, ChatSessionSerializer,
    ChatMessageSerializer, ChatQuerySerializer, ChatResponseSerializer
)
from .tasks import process_document_task
from .services import ChatService, DocumentService

logger = logging.getLogger(__name__)


def index(request):
    """Main chatbot interface"""
    return render(request, 'chatbot_app/index.html')


class DocumentViewSet(viewsets.ModelViewSet):
    """ViewSet for managing documents"""
    queryset = Document.objects.all()
    serializer_class = DocumentSerializer
    parser_classes = (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>)
    
    def get_serializer_class(self):
        if self.action == 'create':
            return DocumentUploadSerializer
        return DocumentSerializer
    
    def create(self, request, *args, **kwargs):
        """Upload and process a new document"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # Save the document
        document = serializer.save(
            uploaded_by=request.user if request.user.is_authenticated else None,
            file_type=serializer.validated_data['file'].name.split('.')[-1].lower()
        )
        
        # Start background processing
        try:
            process_document_task.delay(document.id)
            logger.info(f"Started processing document {document.id}")
        except Exception as e:
            logger.error(f"Failed to start document processing: {e}")
            document.processing_status = 'failed'
            document.save()
        
        return Response(
            DocumentSerializer(document).data,
            status=status.HTTP_201_CREATED
        )
    
    @action(detail=True, methods=['post'])
    def reprocess(self, request, pk=None):
        """Reprocess a document"""
        document = self.get_object()
        document.processing_status = 'pending'
        document.processed = False
        document.save()
        
        try:
            process_document_task.delay(document.id)
            return Response({'status': 'reprocessing started'})
        except Exception as e:
            logger.error(f"Failed to start document reprocessing: {e}")
            return Response(
                {'error': 'Failed to start reprocessing'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def processing_status(self, request):
        """Get processing status of all documents"""
        documents = self.get_queryset()
        status_data = []
        
        for doc in documents:
            status_data.append({
                'id': doc.id,
                'title': doc.title,
                'status': doc.processing_status,
                'processed': doc.processed,
                'chunk_count': doc.chunk_count
            })
        
        return Response(status_data)


class ChatSessionViewSet(viewsets.ModelViewSet):
    """ViewSet for managing chat sessions"""
    queryset = ChatSession.objects.all()
    serializer_class = ChatSessionSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        if self.request.user.is_authenticated:
            queryset = queryset.filter(user=self.request.user)
        return queryset
    
    def create(self, request, *args, **kwargs):
        """Create a new chat session"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        session = serializer.save(
            user=request.user if request.user.is_authenticated else None
        )
        
        return Response(
            ChatSessionSerializer(session).data,
            status=status.HTTP_201_CREATED
        )
    
    @action(detail=True, methods=['post'])
    def end_session(self, request, pk=None):
        """End a chat session"""
        session = self.get_object()
        session.is_active = False
        session.save()
        
        return Response({'status': 'session ended'})


@api_view(['POST'])
@csrf_exempt
def chat_query(request):
    """Handle chat queries"""
    start_time = time.time()
    
    serializer = ChatQuerySerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    data = serializer.validated_data
    message = data['message']
    session_id = data.get('session_id')
    include_sources = data.get('include_sources', True)
    max_sources = data.get('max_sources', 3)
    
    try:
        # Get or create chat session
        if session_id:
            try:
                session = ChatSession.objects.get(id=session_id, is_active=True)
            except ChatSession.DoesNotExist:
                session = ChatSession.objects.create(
                    user=request.user if request.user.is_authenticated else None
                )
        else:
            session = ChatSession.objects.create(
                user=request.user if request.user.is_authenticated else None
            )
        
        # Save user message
        user_message = ChatMessage.objects.create(
            session=session,
            message_type='user',
            content=message
        )
        
        # Process the query using ChatService
        chat_service = ChatService()
        response_data = chat_service.process_query(
            message, 
            session_id=session.id,
            include_sources=include_sources,
            max_sources=max_sources
        )
        
        # Save assistant response
        assistant_message = ChatMessage.objects.create(
            session=session,
            message_type='assistant',
            content=response_data['response'],
            sources=response_data.get('sources', []),
            confidence_score=response_data.get('confidence_score')
        )
        
        # Update session
        session.save()  # This will update the updated_at field
        
        processing_time = time.time() - start_time
        
        response_serializer = ChatResponseSerializer({
            'response': response_data['response'],
            'session_id': session.id,
            'sources': response_data.get('sources', []) if include_sources else [],
            'confidence_score': response_data.get('confidence_score'),
            'processing_time': processing_time
        })
        
        return Response(response_serializer.data)
        
    except Exception as e:
        logger.error(f"Error processing chat query: {e}")
        return Response(
            {'error': 'Failed to process query'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
def health_check(request):
    """Health check endpoint"""
    try:
        # Check database connection
        Document.objects.count()
        
        # Check ChromaDB connection
        from .chromadb_utils import ChromaDBManager
        chroma_manager = ChromaDBManager()
        collection_info = chroma_manager.get_collection_info()
        
        return Response({
            'status': 'healthy',
            'database': 'connected',
            'chromadb': 'connected',
            'document_count': Document.objects.count(),
            'processed_documents': Document.objects.filter(processed=True).count(),
            'vector_count': collection_info.get('count', 0)
        })
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return Response(
            {'status': 'unhealthy', 'error': str(e)},
            status=status.HTTP_503_SERVICE_UNAVAILABLE
        )
