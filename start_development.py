#!/usr/bin/env python
"""
Development startup script for AI Agent Chatbot
This script helps start all necessary services for development
"""

import os
import sys
import subprocess
import time
import signal
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_agent_project.settings')

class DevelopmentServer:
    def __init__(self):
        self.processes = []
        self.running = True
    
    def signal_handler(self, signum, frame):
        """Handle Ctrl+C gracefully"""
        print("\n\nShutting down services...")
        self.running = False
        self.stop_all_processes()
        sys.exit(0)
    
    def start_process(self, command, name, cwd=None):
        """Start a subprocess and add it to the process list"""
        try:
            print(f"Starting {name}...")
            process = subprocess.Popen(
                command,
                shell=True,
                cwd=cwd or project_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            self.processes.append((process, name))
            print(f"✓ {name} started (PID: {process.pid})")
            return process
        except Exception as e:
            print(f"✗ Failed to start {name}: {e}")
            return None
    
    def stop_all_processes(self):
        """Stop all running processes"""
        for process, name in self.processes:
            try:
                print(f"Stopping {name}...")
                process.terminate()
                process.wait(timeout=5)
                print(f"✓ {name} stopped")
            except subprocess.TimeoutExpired:
                print(f"Force killing {name}...")
                process.kill()
            except Exception as e:
                print(f"Error stopping {name}: {e}")
    
    def check_requirements(self):
        """Check if all requirements are met"""
        print("Checking requirements...")
        
        # Check if virtual environment is activated
        if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            print("⚠️  Warning: Virtual environment not detected. Consider using a virtual environment.")
        
        # Check if Redis is available
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0)
            r.ping()
            print("✓ Redis is running")
        except Exception:
            print("✗ Redis is not running. Please start Redis server first.")
            print("  Install Redis: https://redis.io/download")
            return False
        
        # Check if required packages are installed
        required_packages = ['django', 'celery', 'openai', 'chromadb']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"✗ Missing packages: {', '.join(missing_packages)}")
            print("  Run: pip install -r requirements.txt")
            return False
        
        print("✓ All requirements met")
        return True
    
    def setup_database(self):
        """Setup database if needed"""
        print("Setting up database...")
        
        # Check if migrations are needed
        try:
            result = subprocess.run(
                [sys.executable, 'manage.py', 'showmigrations', '--plan'],
                cwd=project_dir,
                capture_output=True,
                text=True
            )
            
            if '[ ]' in result.stdout:  # Unapplied migrations
                print("Applying database migrations...")
                subprocess.run(
                    [sys.executable, 'manage.py', 'migrate'],
                    cwd=project_dir,
                    check=True
                )
                print("✓ Database migrations applied")
            else:
                print("✓ Database is up to date")
                
        except subprocess.CalledProcessError as e:
            print(f"✗ Database setup failed: {e}")
            return False
        
        return True
    
    def create_superuser_if_needed(self):
        """Create superuser if none exists"""
        try:
            # Check if any superuser exists
            result = subprocess.run(
                [sys.executable, 'manage.py', 'shell', '-c', 
                 'from django.contrib.auth.models import User; print(User.objects.filter(is_superuser=True).exists())'],
                cwd=project_dir,
                capture_output=True,
                text=True
            )
            
            if 'False' in result.stdout:
                print("\nNo superuser found. Creating one...")
                print("Please enter superuser details:")
                subprocess.run(
                    [sys.executable, 'manage.py', 'createsuperuser'],
                    cwd=project_dir
                )
        except Exception as e:
            print(f"Note: Could not check/create superuser: {e}")
    
    def start_services(self):
        """Start all development services"""
        print("Starting AI Agent Chatbot Development Server")
        print("=" * 50)
        
        # Setup signal handler for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        
        # Check requirements
        if not self.check_requirements():
            return False
        
        # Setup database
        if not self.setup_database():
            return False
        
        # Create superuser if needed
        self.create_superuser_if_needed()
        
        # Start Celery worker
        celery_worker = self.start_process(
            f"{sys.executable} -m celery -A celery_app worker --loglevel=info",
            "Celery Worker"
        )
        
        # Wait a moment for Celery to start
        time.sleep(2)
        
        # Start Django development server
        django_server = self.start_process(
            f"{sys.executable} manage.py runserver 0.0.0.0:8000",
            "Django Server"
        )
        
        if not django_server:
            return False
        
        print("\n" + "=" * 50)
        print("🚀 AI Agent Chatbot is running!")
        print("📱 Web Interface: http://localhost:8000/")
        print("🔧 Admin Interface: http://localhost:8000/admin/")
        print("📚 API Documentation: http://localhost:8000/api/")
        print("\nPress Ctrl+C to stop all services")
        print("=" * 50)
        
        # Keep the script running and monitor processes
        try:
            while self.running:
                time.sleep(1)
                
                # Check if any process has died
                for process, name in self.processes:
                    if process.poll() is not None:
                        print(f"⚠️  {name} has stopped unexpectedly")
                        
        except KeyboardInterrupt:
            pass
        
        return True

def main():
    """Main function"""
    server = DevelopmentServer()
    
    if len(sys.argv) > 1 and sys.argv[1] == '--help':
        print("AI Agent Chatbot Development Server")
        print("Usage: python start_development.py")
        print("\nThis script will:")
        print("- Check requirements")
        print("- Setup database")
        print("- Start Celery worker")
        print("- Start Django development server")
        print("\nMake sure Redis is running before starting!")
        return
    
    success = server.start_services()
    
    if not success:
        print("\n❌ Failed to start development server")
        sys.exit(1)

if __name__ == '__main__':
    main()
