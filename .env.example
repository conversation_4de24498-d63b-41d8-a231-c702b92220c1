# Django settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database (optional - defaults to SQLite)
# DATABASE_URL=postgresql://user:password@localhost:5432/ai_agent_db

# OpenAI API
OPENAI_API_KEY=your-openai-api-key-here

# Redis (for Celery)
REDIS_URL=redis://localhost:6379/0

# ChromaDB settings
CHROMA_DB_PATH=./chroma_db
CHROMA_COLLECTION_NAME=company_documents

# AI Model settings
EMBEDDING_MODEL=text-embedding-ada-002
CHAT_MODEL=gpt-3.5-turbo

# File upload settings
MAX_UPLOAD_SIZE=10485760  # 10MB in bytes
