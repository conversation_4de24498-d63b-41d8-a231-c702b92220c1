import chromadb
from chromadb.config import Settings
from django.conf import settings
import openai
import logging
from typing import List, Dict, Any
import os

logger = logging.getLogger(__name__)


class ChromaDBManager:
    """
    Manager class for ChromaDB operations
    """
    
    def __init__(self):
        self.client = None
        self.collection = None
        self._initialize_client()
        self._initialize_collection()
    
    def _initialize_client(self):
        """Initialize ChromaDB client"""
        try:
            # Create ChromaDB client with persistent storage
            self.client = chromadb.PersistentClient(
                path=str(settings.CHROMA_DB_PATH),
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            logger.info("ChromaDB client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize ChromaDB client: {e}")
            raise
    
    def _initialize_collection(self):
        """Initialize or get the collection"""
        try:
            # Try to get existing collection
            try:
                self.collection = self.client.get_collection(
                    name=settings.CHROMA_COLLECTION_NAME
                )
                logger.info(f"Retrieved existing collection: {settings.CHROMA_COLLECTION_NAME}")
            except Exception:
                # Create new collection if it doesn't exist
                self.collection = self.client.create_collection(
                    name=settings.CHROMA_COLLECTION_NAME,
                    metadata={"description": "Company documents for AI chatbot"}
                )
                logger.info(f"Created new collection: {settings.CHROMA_COLLECTION_NAME}")
                
        except Exception as e:
            logger.error(f"Failed to initialize collection: {e}")
            raise
    
    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for a list of texts using OpenAI
        """
        try:
            if not settings.OPENAI_API_KEY:
                raise ValueError("OpenAI API key not configured")
            
            openai.api_key = settings.OPENAI_API_KEY
            
            # Generate embeddings
            response = openai.Embedding.create(
                input=texts,
                model=settings.EMBEDDING_MODEL
            )
            
            embeddings = [item['embedding'] for item in response['data']]
            return embeddings
            
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {e}")
            raise
    
    def add_documents(self, documents: List[Dict[str, Any]]):
        """
        Add documents to the ChromaDB collection
        
        Args:
            documents: List of dictionaries with 'id', 'content', and 'metadata' keys
        """
        try:
            if not documents:
                return
            
            # Extract data
            ids = [doc['id'] for doc in documents]
            contents = [doc['content'] for doc in documents]
            metadatas = [doc['metadata'] for doc in documents]
            
            # Generate embeddings
            embeddings = self.generate_embeddings(contents)
            
            # Add to collection
            self.collection.add(
                ids=ids,
                documents=contents,
                metadatas=metadatas,
                embeddings=embeddings
            )
            
            logger.info(f"Added {len(documents)} documents to ChromaDB")
            
        except Exception as e:
            logger.error(f"Failed to add documents to ChromaDB: {e}")
            raise
    
    def update_documents(self, documents: List[Dict[str, Any]]):
        """
        Update existing documents in the ChromaDB collection
        """
        try:
            if not documents:
                return
            
            # Extract data
            ids = [doc['id'] for doc in documents]
            contents = [doc['content'] for doc in documents]
            metadatas = [doc['metadata'] for doc in documents]
            
            # Generate embeddings
            embeddings = self.generate_embeddings(contents)
            
            # Update in collection
            self.collection.update(
                ids=ids,
                documents=contents,
                metadatas=metadatas,
                embeddings=embeddings
            )
            
            logger.info(f"Updated {len(documents)} documents in ChromaDB")
            
        except Exception as e:
            logger.error(f"Failed to update documents in ChromaDB: {e}")
            raise
    
    def search_similar_documents(self, query: str, n_results: int = 5) -> Dict[str, Any]:
        """
        Search for similar documents using vector similarity
        
        Args:
            query: Search query
            n_results: Number of results to return
            
        Returns:
            Dictionary with search results
        """
        try:
            # Generate embedding for the query
            query_embedding = self.generate_embeddings([query])[0]
            
            # Search in collection
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results,
                include=['documents', 'metadatas', 'distances']
            )
            
            # Format results
            formatted_results = []
            if results['documents'] and results['documents'][0]:
                for i in range(len(results['documents'][0])):
                    formatted_results.append({
                        'id': results['ids'][0][i],
                        'content': results['documents'][0][i],
                        'metadata': results['metadatas'][0][i],
                        'similarity_score': 1 - results['distances'][0][i]  # Convert distance to similarity
                    })
            
            return {
                'query': query,
                'results': formatted_results,
                'total_results': len(formatted_results)
            }
            
        except Exception as e:
            logger.error(f"Failed to search documents: {e}")
            raise
    
    def delete_documents(self, document_ids: List[str]):
        """
        Delete documents from the collection
        """
        try:
            self.collection.delete(ids=document_ids)
            logger.info(f"Deleted {len(document_ids)} documents from ChromaDB")
            
        except Exception as e:
            logger.error(f"Failed to delete documents: {e}")
            raise
    
    def get_collection_info(self) -> Dict[str, Any]:
        """
        Get information about the collection
        """
        try:
            count = self.collection.count()
            return {
                'name': settings.CHROMA_COLLECTION_NAME,
                'count': count,
                'status': 'healthy'
            }
            
        except Exception as e:
            logger.error(f"Failed to get collection info: {e}")
            return {
                'name': settings.CHROMA_COLLECTION_NAME,
                'count': 0,
                'status': 'error',
                'error': str(e)
            }
    
    def reset_collection(self):
        """
        Reset the collection (delete all data)
        """
        try:
            self.client.delete_collection(settings.CHROMA_COLLECTION_NAME)
            self._initialize_collection()
            logger.info("Collection reset successfully")
            
        except Exception as e:
            logger.error(f"Failed to reset collection: {e}")
            raise
