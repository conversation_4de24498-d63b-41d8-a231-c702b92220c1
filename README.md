# AI Agent Chatbot - Company Manual Assistant

An intelligent chatbot system that uses company manuals and documents to answer user queries using AI and vector search technology.

## Features

- **Document Upload & Processing**: Upload PDF, DOCX, TXT, and MD files
- **Intelligent Chat**: AI-powered responses based on uploaded documents
- **Vector Search**: ChromaDB for semantic document search
- **Background Processing**: Celery for document processing tasks
- **Admin Interface**: Django admin for managing documents and chat sessions
- **REST API**: Full API for integration with other systems
- **Real-time Updates**: Live status updates for document processing

## Technology Stack

- **Backend**: Django 4.2, Django REST Framework
- **AI/ML**: OpenAI GPT-3.5/4, ChromaDB, Sentence Transformers
- **Database**: SQLite (default) / PostgreSQL
- **Background Tasks**: Celery with Redis
- **Frontend**: Bootstrap 5, Vanilla JavaScript
- **Document Processing**: PyPDF2, python-docx

## Installation

### Prerequisites

- Python 3.8+
- Redis server
- OpenAI API key

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ai-agent-chatbot
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Environment configuration**
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

5. **Database setup**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   python manage.py createsuperuser
   ```

6. **Create required directories**
   ```bash
   mkdir media
   mkdir static
   mkdir chroma_db
   ```

## Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
SECRET_KEY=your-secret-key-here
DEBUG=True
OPENAI_API_KEY=your-openai-api-key-here
REDIS_URL=redis://localhost:6379/0
```

### OpenAI API Key

1. Get your API key from [OpenAI Platform](https://platform.openai.com/)
2. Add it to your `.env` file
3. Ensure you have sufficient credits for embeddings and chat completions

## Running the Application

### Development Mode

1. **Start Redis server**
   ```bash
   redis-server
   ```

2. **Start Celery worker** (in a new terminal)
   ```bash
   celery -A celery_app worker --loglevel=info
   ```

3. **Start Celery beat** (in a new terminal, optional for periodic tasks)
   ```bash
   celery -A celery_app beat --loglevel=info
   ```

4. **Start Django development server**
   ```bash
   python manage.py runserver
   ```

5. **Access the application**
   - Main interface: http://localhost:8000/
   - Admin interface: http://localhost:8000/admin/
   - API documentation: http://localhost:8000/api/

## Usage

### Uploading Documents

1. Navigate to the main interface
2. Use the file upload area to upload company documents
3. Supported formats: PDF, DOCX, TXT, MD (max 10MB)
4. Documents are automatically processed in the background

### Chatting with AI

1. Wait for documents to be processed (status shown in document list)
2. Type your question in the chat input
3. AI will respond based on the uploaded documents
4. Sources are shown with each response for transparency

### Admin Interface

1. Access `/admin/` to manage:
   - Documents and their processing status
   - Chat sessions and messages
   - System configuration
   - User management

## API Endpoints

### Documents
- `GET /api/documents/` - List all documents
- `POST /api/documents/` - Upload new document
- `GET /api/documents/{id}/` - Get document details
- `POST /api/documents/{id}/reprocess/` - Reprocess document

### Chat
- `POST /api/chat/` - Send chat message
- `GET /api/sessions/` - List chat sessions
- `POST /api/sessions/` - Create new session

### System
- `GET /api/health/` - Health check

## Project Structure

```
ai-agent-chatbot/
├── manage.py
├── requirements.txt
├── README.md
├── .env.example
├── celery_app.py
├── chromadb_utils.py
├── ai_agent_project/
│   ├── __init__.py
│   ├── settings.py
│   ├── urls.py
│   ├── wsgi.py
│   └── asgi.py
├── chatbot_app/
│   ├── __init__.py
│   ├── admin.py
│   ├── apps.py
│   ├── models.py
│   ├── views.py
│   ├── urls.py
│   ├── serializers.py
│   ├── services.py
│   ├── tasks.py
│   └── templates/
│       └── chatbot_app/
│           └── index.html
├── templates/
│   └── base.html
├── static/
├── media/
└── chroma_db/
```

## Troubleshooting

### Common Issues

1. **ChromaDB connection errors**
   - Ensure the `chroma_db` directory exists
   - Check file permissions

2. **OpenAI API errors**
   - Verify API key is correct
   - Check API usage limits and billing

3. **Celery worker not processing**
   - Ensure Redis is running
   - Check Celery worker logs

4. **Document processing fails**
   - Check file format and size
   - Verify document is not corrupted

### Logs

Check logs in:
- Django: Console output
- Celery: Worker terminal output
- ChromaDB: Application logs

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
