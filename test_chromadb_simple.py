#!/usr/bin/env python
"""
Simple test for ChromaDB functionality without OpenAI embeddings
"""

import chromadb
from chromadb.config import Settings
import numpy as np
import os

def test_chromadb_basic():
    """Test basic ChromaDB functionality with dummy embeddings"""
    print("Testing ChromaDB with dummy embeddings...")
    
    try:
        # Create ChromaDB client
        client = chromadb.PersistentClient(
            path="./test_chroma_db",
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        print("✓ ChromaDB client created")
        
        # Create or get collection
        try:
            collection = client.get_collection("test_collection")
            print("✓ Retrieved existing collection")
        except:
            collection = client.create_collection("test_collection")
            print("✓ Created new collection")
        
        # Test data
        documents = [
            "This is a test document about AI and machine learning.",
            "Python is a great programming language for data science.",
            "ChromaDB is a vector database for AI applications."
        ]
        
        ids = ["doc1", "doc2", "doc3"]
        
        # Generate dummy embeddings (normally would use OpenAI)
        embeddings = []
        for doc in documents:
            # Create a simple embedding based on document length and content
            embedding = np.random.rand(384).tolist()  # 384-dimensional vector
            embeddings.append(embedding)
        
        # Add documents to collection
        collection.add(
            documents=documents,
            embeddings=embeddings,
            ids=ids,
            metadatas=[
                {"source": "test", "type": "ai"},
                {"source": "test", "type": "programming"},
                {"source": "test", "type": "database"}
            ]
        )
        print("✓ Documents added to collection")
        
        # Test querying
        query_embedding = np.random.rand(384).tolist()
        results = collection.query(
            query_embeddings=[query_embedding],
            n_results=2
        )
        
        print(f"✓ Query results: Found {len(results['documents'][0])} documents")
        for i, doc in enumerate(results['documents'][0]):
            print(f"  - Document {i+1}: {doc[:50]}...")
        
        # Test collection info
        count = collection.count()
        print(f"✓ Collection contains {count} documents")
        
        # Clean up
        client.delete_collection("test_collection")
        print("✓ Test collection cleaned up")
        
        return True
        
    except Exception as e:
        print(f"✗ ChromaDB test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_document_processing_pipeline():
    """Test the complete document processing pipeline without OpenAI"""
    print("\nTesting document processing pipeline...")
    
    try:
        # Simulate document upload and processing
        sample_text = """
        Company Policy Manual
        
        1. Introduction
        This manual contains important policies and procedures for all employees.
        
        2. Work Hours
        Standard work hours are 9 AM to 5 PM, Monday through Friday.
        
        3. Remote Work Policy
        Employees may work remotely up to 2 days per week with manager approval.
        
        4. Leave Policy
        All employees are entitled to 20 days of paid vacation per year.
        """
        
        # Split text into chunks (simulating document processing)
        chunks = []
        paragraphs = sample_text.strip().split('\n\n')
        for i, paragraph in enumerate(paragraphs):
            if paragraph.strip():
                chunks.append({
                    'id': f'chunk_{i}',
                    'content': paragraph.strip(),
                    'metadata': {
                        'document_title': 'Company Policy Manual',
                        'chunk_index': i,
                        'source': 'manual'
                    }
                })
        
        print(f"✓ Document split into {len(chunks)} chunks")
        
        # Create ChromaDB client for document storage
        client = chromadb.PersistentClient(
            path="./test_documents_db",
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # Create collection for documents
        try:
            collection = client.get_collection("company_documents")
            client.delete_collection("company_documents")
        except:
            pass
        
        collection = client.create_collection("company_documents")
        print("✓ Document collection created")
        
        # Add chunks with dummy embeddings
        ids = [chunk['id'] for chunk in chunks]
        contents = [chunk['content'] for chunk in chunks]
        metadatas = [chunk['metadata'] for chunk in chunks]
        
        # Generate dummy embeddings
        embeddings = [np.random.rand(384).tolist() for _ in chunks]
        
        collection.add(
            ids=ids,
            documents=contents,
            embeddings=embeddings,
            metadatas=metadatas
        )
        print("✓ Document chunks stored in vector database")
        
        # Test search functionality
        query = "What are the work hours?"
        query_embedding = np.random.rand(384).tolist()
        
        search_results = collection.query(
            query_embeddings=[query_embedding],
            n_results=3,
            include=['documents', 'metadatas', 'distances']
        )
        
        print(f"✓ Search query: '{query}'")
        print(f"✓ Found {len(search_results['documents'][0])} relevant chunks:")
        
        for i, (doc, metadata) in enumerate(zip(search_results['documents'][0], search_results['metadatas'][0])):
            print(f"  {i+1}. {doc[:100]}...")
            print(f"     Source: {metadata.get('document_title', 'Unknown')}")
        
        # Clean up
        client.delete_collection("company_documents")
        print("✓ Document collection cleaned up")
        
        return True
        
    except Exception as e:
        print(f"✗ Document processing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("ChromaDB Vector Storage Test")
    print("=" * 40)
    
    # Test basic ChromaDB functionality
    basic_test = test_chromadb_basic()
    
    # Test document processing pipeline
    pipeline_test = test_document_processing_pipeline()
    
    print("\n" + "=" * 40)
    print("Test Summary:")
    print(f"Basic ChromaDB: {'✓' if basic_test else '✗'}")
    print(f"Document Pipeline: {'✓' if pipeline_test else '✗'}")
    
    if basic_test and pipeline_test:
        print("\n🎉 ChromaDB vector storage is working correctly!")
        print("\nThe system can:")
        print("- Store documents as vector embeddings")
        print("- Search for similar content")
        print("- Manage document collections")
        print("\nTo enable full functionality:")
        print("1. Add your OpenAI API key to .env file")
        print("2. The system will then generate real embeddings")
        return True
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == '__main__':
    main()
